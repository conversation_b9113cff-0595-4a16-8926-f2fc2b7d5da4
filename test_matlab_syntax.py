#!/usr/bin/env python3
"""
Test MATLAB syntax and basic functionality of the HUD pre-compensation system.

This script performs basic syntax checking and validates that the MATLAB files
are properly structured and contain the expected functions.
"""

import os
import re

def check_matlab_syntax(filename):
    """Check basic MATLAB syntax in a file."""
    print(f"Checking {filename}...")
    
    if not os.path.exists(filename):
        print(f"  ERROR: File {filename} not found")
        return False
    
    with open(filename, 'r') as f:
        content = f.read()
    
    errors = []
    warnings = []
    
    # Check for basic syntax issues
    lines = content.split('\n')
    
    # Check for function definition
    function_pattern = r'^\s*function\s+.*=\s*\w+\s*\('
    has_function = any(re.match(function_pattern, line) for line in lines)
    
    if filename.endswith('.m') and not has_function and 'main_workflow' not in filename:
        warnings.append("No function definition found")
    
    # Check for unmatched parentheses, brackets, braces
    paren_count = content.count('(') - content.count(')')
    bracket_count = content.count('[') - content.count(']')
    brace_count = content.count('{') - content.count('}')
    
    if paren_count != 0:
        errors.append(f"Unmatched parentheses: {paren_count}")
    if bracket_count != 0:
        errors.append(f"Unmatched brackets: {bracket_count}")
    if brace_count != 0:
        errors.append(f"Unmatched braces: {brace_count}")
    
    # Check for incomplete statements (lines ending with ...)
    incomplete_lines = [i+1 for i, line in enumerate(lines) 
                       if line.strip().endswith('...') and 
                       i+1 < len(lines) and not lines[i+1].strip()]
    
    if incomplete_lines:
        errors.append(f"Incomplete statements at lines: {incomplete_lines}")
    
    # Check for missing semicolons in function calls (potential issues)
    for i, line in enumerate(lines):
        stripped = line.strip()
        if (stripped and not stripped.startswith('%') and 
            not stripped.endswith(';') and not stripped.endswith(',') and
            '=' in stripped and 'fprintf' not in stripped and
            'if ' not in stripped and 'for ' not in stripped and
            'while ' not in stripped and 'function ' not in stripped and
            'end' != stripped and 'else' != stripped and 'elseif' not in stripped):
            # This might be a statement that should end with semicolon
            pass  # Not enforcing this as it's style preference
    
    # Report results
    if errors:
        print(f"  ERRORS found:")
        for error in errors:
            print(f"    - {error}")
        return False
    
    if warnings:
        print(f"  WARNINGS:")
        for warning in warnings:
            print(f"    - {warning}")
    
    print(f"  PASS: Basic syntax check completed")
    return True

def check_function_completeness():
    """Check that all required functions are implemented."""
    print("\nChecking function completeness...")
    
    # Check offline_calculate_all_prekernels.m
    if os.path.exists('offline_calculate_all_prekernels.m'):
        with open('offline_calculate_all_prekernels.m', 'r') as f:
            content = f.read()
        
        required_elements = [
            'function.*offline_stage_auto',
            'deconvolve_wiener',
            'rect_source',
            'delta_target',
            'helper_downsample_kernel',
            'save.*prekernel_map_lowres'
        ]
        
        missing = []
        for element in required_elements:
            if not re.search(element, content):
                missing.append(element)
        
        if missing:
            print(f"  ERROR: Missing elements in offline stage: {missing}")
            return False
        else:
            print(f"  PASS: Offline stage contains all required elements")
    
    # Check online_apply_compensation.m
    if os.path.exists('online_apply_compensation.m'):
        with open('online_apply_compensation.m', 'r') as f:
            content = f.read()
        
        required_elements = [
            'function.*online_stage_auto',
            'imfilter',
            'scatteredInterpolant',
            'candidate_images'
        ]
        
        missing = []
        for element in required_elements:
            if not re.search(element, content):
                missing.append(element)
        
        if missing:
            print(f"  ERROR: Missing elements in online stage: {missing}")
            return False
        else:
            print(f"  PASS: Online stage contains all required elements")
    
    return True

def check_data_files():
    """Check that required data files exist."""
    print("\nChecking data files...")
    
    # Check BSF data directory
    if os.path.exists('bsf_data'):
        bsf_files = [f for f in os.listdir('bsf_data') if f.startswith('bsf_') and f.endswith('.mat')]
        if len(bsf_files) == 36:
            print(f"  PASS: Found all 36 BSF data files")
        else:
            print(f"  WARNING: Found {len(bsf_files)} BSF files, expected 36")
    else:
        print(f"  WARNING: bsf_data directory not found")
    
    # Check UI image
    if os.path.exists('ui_images/test_arrow.png'):
        print(f"  PASS: Test UI image found")
    else:
        print(f"  WARNING: Test UI image not found")
    
    return True

def check_documentation_consistency():
    """Check that documentation matches implementation."""
    print("\nChecking documentation consistency...")
    
    # Check that function names match documentation
    expected_files = [
        'main_workflow.m',
        'offline_calculate_all_prekernels.m',
        'online_apply_compensation.m',
        'helper_parse_speos_data.m',
        'helper_downsample_kernel.m'
    ]
    
    missing_files = [f for f in expected_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"  ERROR: Missing files: {missing_files}")
        return False
    else:
        print(f"  PASS: All expected files present")
    
    # Check function names in main_workflow.m
    if os.path.exists('main_workflow.m'):
        with open('main_workflow.m', 'r') as f:
            content = f.read()
        
        if 'offline_stage_auto' in content and 'online_stage_auto' in content:
            print(f"  PASS: Function names are consistent")
        else:
            print(f"  ERROR: Function names inconsistent with implementation")
            return False
    
    return True

def main():
    """Run all tests."""
    print("=== MATLAB HUD PRE-COMPENSATION SYSTEM VALIDATION ===")
    print("Testing syntax, completeness, and consistency...\n")
    
    # List of MATLAB files to check
    matlab_files = [
        'main_workflow.m',
        'offline_calculate_all_prekernels.m',
        'online_apply_compensation.m',
        'helper_parse_speos_data.m',
        'helper_downsample_kernel.m'
    ]
    
    # Check syntax for each file
    syntax_ok = True
    for filename in matlab_files:
        if not check_matlab_syntax(filename):
            syntax_ok = False
    
    # Check function completeness
    completeness_ok = check_function_completeness()
    
    # Check data files
    data_ok = check_data_files()
    
    # Check documentation consistency
    docs_ok = check_documentation_consistency()
    
    # Summary
    print("\n=== VALIDATION SUMMARY ===")
    print(f"Syntax Check: {'PASS' if syntax_ok else 'FAIL'}")
    print(f"Function Completeness: {'PASS' if completeness_ok else 'FAIL'}")
    print(f"Data Files: {'PASS' if data_ok else 'PARTIAL'}")
    print(f"Documentation Consistency: {'PASS' if docs_ok else 'FAIL'}")
    
    if syntax_ok and completeness_ok and docs_ok:
        print("\n✓ All critical issues have been resolved!")
        print("✓ The system is ready for testing with MATLAB")
        print("\nNext steps:")
        print("1. Open MATLAB")
        print("2. Navigate to this directory")
        print("3. Run: main_workflow")
        print("4. Or run: test_system for comprehensive testing")
    else:
        print("\n✗ Some issues remain. Please review the errors above.")
    
    return syntax_ok and completeness_ok and docs_ok

if __name__ == '__main__':
    main()
