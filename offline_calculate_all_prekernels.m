function offline_stage_auto(simulation_file, map_file, grid_dims, ...
    grid_centers_highres_coords, oversampling_rate, matrix_size, noise_r1, noise_r2)

    % --- STEP 1: PARSE RAW DATA TO GET BSFs AND COORDINATES ---
    [bsf_cells, precise_coords_highres] = helper_parse_speos_data(...
        simulation_file, grid_centers_highres_coords, matrix_size);
    
    num_kernels = prod(grid_dims);
    prekernel_map_lowres = cell(grid_dims);

    % (The section for defining rect_source, delta_target, deconvolve_wiener is the same)
    % ...

    for i = 1:num_kernels
        BSF_from_speos = bsf_cells{i};
        BSF_from_speos = BSF_from_speos / sum(BSF_from_speos(:));
        % (The core logic for PSF estimation and Pre-Kernel generation is the same)
        % ...
        pre_kernel_high_res = ...; % Calculate high-res kernel

        low_res_kernel = helper_downsample_kernel(pre_kernel_high_res, oversampling_rate);
        
        [row, col] = ind2sub(grid_dims, i);
        prekernel_map_lowres{row, col} = low_res_kernel;
    end

    % --- SAVE KERNELS AND COORDINATES ---
    % Convert high-res simulation coordinates to low-res UI image coordinates
    grid_centers_lowres_coords = precise_coords_highres / oversampling_rate;
    
    save(map_file, 'prekernel_map_lowres', 'grid_centers_lowres_coords', 'grid_dims');
    fprintf('  - Automated LOW-RES PreKernel map with coordinates saved to "%s".\n', map_file);
end
