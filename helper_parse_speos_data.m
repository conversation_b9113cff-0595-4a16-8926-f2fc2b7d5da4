function [bsf_cells, precise_coords] = helper_parse_speos_data(...
    data_source, grid_centers_coords, matrix_size)
    % HELPER_PARSE_SPEOS_DATA - Parse BSF data from simulation files
    %
    % This function supports two input formats:
    % 1. Directory path containing individual .mat files (bsf_1.mat, bsf_2.mat, etc.)
    % 2. Single text file with X, Y, Intensity columns
    %
    % Inputs:
    %   data_source - Either a directory path or a single file path
    %   grid_centers_coords - Approximate center coordinates for each BSF
    %   matrix_size - Size of the output BSF matrices
    %
    % Outputs:
    %   bsf_cells - Cell array containing BSF matrices
    %   precise_coords - Refined center coordinates for each BSF

    % Input validation
    if ~exist(data_source, 'file') && ~exist(data_source, 'dir')
        error('Data source "%s" does not exist.', data_source);
    end

    if size(grid_centers_coords, 2) ~= 2
        error('grid_centers_coords must have 2 columns (X, Y coordinates).');
    end

    if matrix_size <= 0 || mod(matrix_size, 1) ~= 0
        error('matrix_size must be a positive integer.');
    end

    num_bsfs = size(grid_centers_coords, 1);
    bsf_cells = cell(1, num_bsfs);
    precise_coords = zeros(num_bsfs, 2);

    fprintf('  - Parsing BSF data from "%s"...\n', data_source);

    % Determine input format and parse accordingly
    if exist(data_source, 'dir')
        % Format 1: Directory with individual .mat files
        fprintf('  - Using directory format with individual .mat files...\n');
        [bsf_cells, precise_coords] = parse_mat_files(data_source, num_bsfs, matrix_size);

    elseif exist(data_source, 'file')
        % Check if it's a .mat file or text file
        [~, ~, ext] = fileparts(data_source);
        if strcmpi(ext, '.mat')
            error('Single .mat file format not supported. Use directory with multiple .mat files or text file.');
        else
            % Format 2: Single text file with X, Y, Intensity columns
            fprintf('  - Using text file format with X, Y, Intensity columns...\n');
            [bsf_cells, precise_coords] = parse_text_file(data_source, grid_centers_coords, matrix_size);
        end
    else
        error('Unable to determine data format for "%s".', data_source);
    end

    % Validate results
    for i = 1:num_bsfs
        if isempty(bsf_cells{i}) || any(size(bsf_cells{i}) ~= matrix_size)
            error('BSF %d has incorrect size or is empty.', i);
        end

        if any(isnan(precise_coords(i, :))) || any(isinf(precise_coords(i, :)))
            warning('BSF %d has invalid coordinates. Using original coordinates.', i);
            precise_coords(i, :) = grid_centers_coords(i, :);
        end
    end

    fprintf('  - Successfully extracted %d BSFs.\n', num_bsfs);
end

function [bsf_cells, precise_coords] = parse_mat_files(data_dir, num_bsfs, matrix_size)
    % Parse BSF data from individual .mat files

    bsf_cells = cell(1, num_bsfs);
    precise_coords = zeros(num_bsfs, 2);

    for i = 1:num_bsfs
        mat_file = fullfile(data_dir, sprintf('bsf_%d.mat', i));

        if ~exist(mat_file, 'file')
            error('Required BSF file "%s" not found.', mat_file);
        end

        try
            data = load(mat_file);
            if ~isfield(data, 'bsf_data')
                error('File "%s" does not contain required variable "bsf_data".', mat_file);
            end

            bsf_data = data.bsf_data;

            % Validate BSF data
            if ~isnumeric(bsf_data) || ndims(bsf_data) ~= 2
                error('BSF data in "%s" must be a 2D numeric matrix.', mat_file);
            end

            % Resize if necessary
            if any(size(bsf_data) ~= matrix_size)
                fprintf('    Resizing BSF %d from %dx%d to %dx%d...\n', ...
                    i, size(bsf_data, 1), size(bsf_data, 2), matrix_size, matrix_size);
                bsf_data = imresize(bsf_data, [matrix_size, matrix_size], 'bilinear');
            end

            % Store normalized BSF
            bsf_sum = sum(bsf_data(:));
            if bsf_sum > eps
                bsf_cells{i} = bsf_data / bsf_sum;
            else
                warning('BSF %d has zero or near-zero sum. Using uniform distribution.', i);
                bsf_cells{i} = ones(matrix_size, matrix_size) / (matrix_size^2);
            end

            % Calculate centroid as precise coordinates
            [X, Y] = meshgrid(1:matrix_size, 1:matrix_size);
            total_intensity = sum(bsf_cells{i}(:));
            if total_intensity > eps
                precise_coords(i, 1) = sum(sum(X .* bsf_cells{i})) / total_intensity;
                precise_coords(i, 2) = sum(sum(Y .* bsf_cells{i})) / total_intensity;
            else
                precise_coords(i, :) = [matrix_size/2, matrix_size/2];
            end

        catch ME
            error('Failed to load BSF file "%s": %s', mat_file, ME.message);
        end
    end
end

function [bsf_cells, precise_coords] = parse_text_file(text_file, grid_centers_coords, matrix_size)
    % Parse BSF data from a single text file with X, Y, Intensity columns

    try
        raw_data = readmatrix(text_file);
    catch ME
        error('Failed to read text file "%s": %s', text_file, ME.message);
    end

    if size(raw_data, 2) < 3
        error('Text file must have at least 3 columns: X, Y, Intensity.');
    end

    sim_x = raw_data(:, 1);
    sim_y = raw_data(:, 2);
    sim_intensity = raw_data(:, 3);

    % Remove invalid data points
    valid_idx = ~isnan(sim_x) & ~isnan(sim_y) & ~isnan(sim_intensity) & sim_intensity >= 0;
    sim_x = sim_x(valid_idx);
    sim_y = sim_y(valid_idx);
    sim_intensity = sim_intensity(valid_idx);

    if isempty(sim_x)
        error('No valid data points found in text file.');
    end

    num_bsfs = size(grid_centers_coords, 1);
    bsf_cells = cell(1, num_bsfs);
    precise_coords = zeros(num_bsfs, 2);
    half_size = floor(matrix_size / 2);

    for i = 1:num_bsfs
        center_x = grid_centers_coords(i, 1);
        center_y = grid_centers_coords(i, 2);

        % Define bounding box around the approximate center
        x_min = center_x - half_size;
        x_max = center_x + half_size;
        y_min = center_y - half_size;
        y_max = center_y + half_size;

        % Find data points within bounding box
        idx = find(sim_x >= x_min & sim_x <= x_max & sim_y >= y_min & sim_y <= y_max);

        if length(idx) < 3
            warning('Insufficient data points for BSF %d. Using uniform distribution.', i);
            bsf_cells{i} = ones(matrix_size, matrix_size) / (matrix_size^2);
            precise_coords(i, :) = [center_x, center_y];
            continue;
        end

        local_x = sim_x(idx);
        local_y = sim_y(idx);
        local_intensity = sim_intensity(idx);

        % Calculate precise center using weighted centroid
        total_intensity = sum(local_intensity);
        if total_intensity > eps
            precise_x = sum(local_x .* local_intensity) / total_intensity;
            precise_y = sum(local_y .* local_intensity) / total_intensity;
        else
            precise_x = center_x;
            precise_y = center_y;
        end
        precise_coords(i, :) = [precise_x, precise_y];

        % Create regular grid for interpolation
        [Xq, Yq] = meshgrid(...
            linspace(precise_x - half_size, precise_x + half_size, matrix_size), ...
            linspace(precise_y - half_size, precise_y + half_size, matrix_size));

        % Interpolate BSF data onto regular grid
        try
            bsf_matrix = griddata(local_x, local_y, local_intensity, Xq, Yq, 'linear');
            bsf_matrix(isnan(bsf_matrix)) = 0;
        catch
            warning('Interpolation failed for BSF %d. Using uniform distribution.', i);
            bsf_matrix = ones(matrix_size, matrix_size);
        end

        % Normalize BSF
        bsf_sum = sum(bsf_matrix(:));
        if bsf_sum > eps
            bsf_cells{i} = bsf_matrix / bsf_sum;
        else
            bsf_cells{i} = ones(matrix_size, matrix_size) / (matrix_size^2);
        end
    end
end
