function [bsf_cells, precise_coords] = helper_parse_speos_data(...
    simulation_file, grid_centers_coords, matrix_size)
    
    fprintf('  - Parsing raw simulation data from "%s"...\n', simulation_file);
    % Assuming the file is a text file with 3 columns: X, Y, Intensity
    % You might need to adjust 'readmatrix' options based on your file format (e.g., delimiters)
    raw_data = readmatrix(simulation_file);
    sim_x = raw_data(:,1);
    sim_y = raw_data(:,2);
    sim_intensity = raw_data(:,3);
    
    num_bsfs = size(grid_centers_coords, 1);
    bsf_cells = cell(1, num_bsfs);
    precise_coords = zeros(num_bsfs, 2);
    
    half_size = floor(matrix_size / 2);
    
    for i = 1:num_bsfs
        center_x = grid_centers_coords(i, 1);
        center_y = grid_centers_coords(i, 2);
        
        % Define a bounding box around the approximate center
        x_min = center_x - half_size; x_max = center_x + half_size;
        y_min = center_y - half_size; y_max = center_y + half_size;
        
        % Find all data points within this bounding box
        idx = find(sim_x >= x_min & sim_x <= x_max & sim_y >= y_min & sim_y <= y_max);
        
        local_x = sim_x(idx);
        local_y = sim_y(idx);
        local_intensity = sim_intensity(idx);
        
        % Use the weighted centroid to find the *precise* center
        precise_x = sum(local_x .* local_intensity) / sum(local_intensity);
        precise_y = sum(local_y .* local_intensity) / sum(local_intensity);
        precise_coords(i, :) = [precise_x, precise_y];
        
        % Define a regular grid to interpolate onto
        [Xq, Yq] = meshgrid(linspace(precise_x - half_size, precise_x + half_size, matrix_size), ...
                            linspace(precise_y - half_size, precise_y + half_size, matrix_size));
        
        % Use griddata to create the BSF matrix
        bsf_matrix = griddata(local_x, local_y, local_intensity, Xq, Yq, 'cubic');
        bsf_matrix(isnan(bsf_matrix)) = 0; % Replace NaNs from interpolation with 0
        bsf_cells{i} = bsf_matrix;
    end
    fprintf('  - Successfully extracted %d BSFs from raw data.\n', num_bsfs);
end
