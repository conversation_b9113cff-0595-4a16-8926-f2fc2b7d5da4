## 1. 环境要求

*   **MATLAB**: 推荐使用 R2020a 或更新版本。
*   **Image Processing Toolbox**: 项目核心依赖 `imfilter` 函数。
*   **(可选) Parallel Computing Toolbox**: 若要使用 `parfor` 进行并行加速，则需要此工具箱。

## 2. 文件结构

请确保您的项目文件夹遵循以下结构：

```
/Your_Project_Folder/
    |
    |-- main_workflow.m                 (主执行脚本)
    |-- offline_stage_final.m           (离线计算最终版函数)
    |-- online_stage_final.m            (在线处理最终版函数)
    |-- helper_downsample_kernel.m      (降采样辅助函数)
    |-- README.md                       (本项目说明文件)
    |
    |-- /bsf_data/                      (存放BSF数据)
    |   |-- bsf_1.mat
    |   |-- bsf_2.mat
    |   |-- ...
    |   |-- bsf_36.mat
    |
    |-- /ui_images/                     (存放要处理的UI图像)
        |-- test_arrow.png
```

## 3. 使用方法

### 第1步: 准备仿真数据

在光学仿真软件中，按 6x6 网格采样36个点。
每个点使用 5x5 像素的方形光源仿真。
成像面使用 3倍过采样率，并用 31x31 或更大的矩阵接收光斑。
将36个BSF结果分别保存为 bsf_1.mat 到 bsf_36.mat，每个文件内包含一个名为 bsf_data 的变量。
将文件放入 bsf_data 文件夹。

### 第2步: 准备输入数据

1.  **BSF 数据**: 在项目根目录下创建 `bsf_data` 文件夹。将BSF数据文件放入其中。
    *   文件必须为 `.mat` 格式。
    *   文件必须按顺序命名为 `bsf_1.mat`, `bsf_2.mat`, ..., `bsf_36.mat`。
    *   每个 `.mat` 文件内部必须包含一个名为 `bsf_data` 的二维矩阵变量。

2.  **UI 图像**: 在项目根目录下创建 `ui_images` 文件夹。将您希望进行预补偿处理的UI图像（如 `test_arrow.png`）放入其中。

### 第3步: 运行主脚本

1.  打开 MATLAB。
2.  在MATLAB的当前文件夹浏览器中，导航到本项目的根目录。
3.  打开 `main_workflow.m` 文件。
4.  可以根据需要调整文件顶部的 `CONFIGURATION` 部分的参数（详见下一节）。
5.  点击“运行”按钮或在命令行中输入 `main_workflow` 并按回车。

脚本将自动执行离线和在线阶段的所有步骤。

### 第4步: 配置参数详解

可以在 `main_workflow.m` 的 `CONFIGURATION` 部分调整以下参数：

*   `bsf_data_folder`: 存放BSF数据的文件夹路径。
*   `ui_image_path`: 要处理的目标UI图像的路径。
*   `output_map_file`: 离线阶段生成的**低分辨率预补偿核地图**的输出文件名。
*   `output_image_file`: 最终生成的预补偿图像的输出文件名。
*   `grid_dims`: 光学仿真采样点的网格布局（例如 `[6, 6]` 表示6x6的网格）。
*   `source_size`, `oversampling_rate`, `matrix_size`: 这些是与光学仿真设置相匹配的参数。
    *   `oversampling_rate`: 成像面分辨率相对于原始图像分辨率的倍数（例如 `3`）。
    *   `matrix_size`: 仿真时用于计算的矩阵大小（例如 `31`x`31`）。
*   `noise_ratio_step1`, `noise_ratio_step2`: 维纳解卷积算法的**正则化参数**。这两个是重要的微调参数，用于在“去模糊”和“抑制噪声”之间取得平衡。

### 第5步: 输出结果说明

运行脚本后，将得到以下输出：

1.  **`prekernel_map_lowres_final.mat`** (文件):
    *   这是**离线阶段的最终产物**。它包含了计算好的、可直接用于在线处理的低分辨率预补偿核地图。
    *   对于一个给定的光学系统，此文件**只需生成一次**。后续处理任何图像时，脚本会自动加载此文件，跳过耗时的离线计算。

2.  **`precompensated_image_final.png`** (图像文件):
    *   这是**在线阶段的最终产物**——经过预补偿的图像。
    *   **注意**: 这张图像在电脑屏幕上直接观看时，可能会显得有些模糊、或带有奇怪的“振铃”效应。这是**正常且正确**的，因为它已经被“预先扭曲”，以抵消真实光学系统带来的模糊。

3.  **一个MATLAB图像窗口**:
    *   此窗口会并排显示处理前的**原始图像**和处理后的**预补偿图像**，便于进行直观的比较。

