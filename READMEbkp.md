### 🤖 Assistant



好的，这是为您的项目生成的一份详细的`README.md`文件。它遵循了标准的开源项目文档格式，清晰地说明了项目的作用、使用方法和技术细节。

将以下内容保存为项目根目录下的 `README.md` 文件即可。

---

# Spatially-Varying HUD Pre-Compensation - Optimized MATLAB Workflow

## 1. 项目概述

本项目实现了一套先进的、经过高度优化的MATLAB工作流，用于校正抬头显示器（HUD）光学系统中存在的**空间变化模糊**（如像散、彗差等）。

其核心目标是生成一张**预补偿（Pre-compensated）**图像。当这张图像通过真实的HUD光学系统投射后，到达驾驶员眼中的最终成像将是清晰、锐利的，有效解决了在整个视场（Field of View）内清晰度不均的问题。

本项目采用两阶段工作流：
*   **离线阶段 (Offline Stage)**: 对给定的光学系统进行一次性的标定，生成一个轻量级的“预补偿核地图”。
*   **在线阶段 (Online Stage)**: 使用该地图，对任意UI图像进行高效、实时的预补偿处理。

## 2. 核心技术特性

本代码实现的是最终优化的“黄金标准”方案，具备以下特点：

*   **先进的BSF解算**: 能从光学仿真软件（如Zemax, SPEOS）直接导出的光束扩散函数（BSF）数据中，精确反解出点扩散函数（PSF）。
*   **能量守恒的核降采样**: 在离线阶段采用理论正确的能量合并（Binning/Summation）方法对高分辨率核进行降采样，确保了算法的准确性。
*   **结果插值法**: 采用“先卷积，后插值”的高效策略，避免了在实时阶段对卷积核的重复计算。
*   **完全向量化**: 在线阶段的插值过程完全基于MATLAB的矩阵运算，**无任何`for`循环**，速度极快。
*   **并行计算支持**: 在线阶段生成候选图像的步骤可利用`parfor`在多核CPU上并行处理，进一步提升性能。

## 3. 环境要求

*   **MATLAB**: 推荐使用 R2020a 或更新版本。
*   **Image Processing Toolbox**: 项目核心依赖 `imfilter` 函数。
*   **(可选) Parallel Computing Toolbox**: 若要使用 `parfor` 进行并行加速，则需要此工具箱。

## 4. 文件结构

请确保您的项目文件夹遵循以下结构：

```
/Your_Project_Folder/
    |
    |-- main_workflow.m                 (主执行脚本)
    |-- offline_stage_final.m           (离线计算最终版函数)
    |-- online_stage_final.m            (在线处理最终版函数)
    |-- helper_downsample_kernel.m      (降采样辅助函数)
    |-- README.md                       (本项目说明文件)
    |
    |-- /bsf_data/                      (存放您的BSF数据)
    |   |-- bsf_1.mat
    |   |-- bsf_2.mat
    |   |-- ...
    |   |-- bsf_36.mat
    |
    |-- /ui_images/                     (存放您要处理的UI图像)
        |-- test_arrow.png
```

## 5. 使用方法

#### **步骤 1: 准备输入数据**

1.  **BSF 数据**: 在项目根目录下创建 `bsf_data` 文件夹。将您的BSF数据文件放入其中。
    *   文件必须为 `.mat` 格式。
    *   文件必须按顺序命名为 `bsf_1.mat`, `bsf_2.mat`, ..., `bsf_36.mat`。
    *   每个 `.mat` 文件内部必须包含一个名为 `bsf_data` 的二维矩阵变量。

2.  **UI 图像**: 在项目根目录下创建 `ui_images` 文件夹。将您希望进行预补偿处理的UI图像（如 `test_arrow.png`）放入其中。

#### **步骤 2: 运行主脚本**

1.  打开 MATLAB。
2.  在MATLAB的当前文件夹浏览器中，导航到本项目的根目录。
3.  打开 `main_workflow.m` 文件。
4.  您可以根据需要调整文件顶部的 `CONFIGURATION` 部分的参数（详见下一节）。
5.  点击“运行”按钮或在命令行中输入 `main_workflow` 并按回车。

脚本将自动执行离线和在线阶段的所有步骤。

## 6. 配置参数详解

您可以在 `main_workflow.m` 的 `CONFIGURATION` 部分调整以下参数：

*   `bsf_data_folder`: 存放BSF数据的文件夹路径。
*   `ui_image_path`: 要处理的目标UI图像的路径。
*   `output_map_file`: 离线阶段生成的**低分辨率预补偿核地图**的输出文件名。
*   `output_image_file`: 最终生成的预补偿图像的输出文件名。
*   `grid_dims`: 您光学仿真采样点的网格布局（例如 `[6, 6]` 表示6x6的网格）。
*   `source_size`, `oversampling_rate`, `matrix_size`: 这些是与您光学仿真设置相匹配的参数。
    *   `oversampling_rate`: 成像面分辨率相对于原始图像分辨率的倍数（例如 `3`）。
    *   `matrix_size`: 仿真时用于计算的矩阵大小（例如 `31`x`31`）。
*   `noise_ratio_step1`, `noise_ratio_step2`: 维纳解卷积算法的**正则化参数**。这两个是重要的微调参数，用于在“去模糊”和“抑制噪声”之间取得平衡。

## 7. 输出结果说明

运行脚本后，您将得到以下输出：

1.  **`prekernel_map_lowres_final.mat`** (文件):
    *   这是**离线阶段的最终产物**。它包含了计算好的、可直接用于在线处理的低分辨率预补偿核地图。
    *   对于一个给定的光学系统，此文件**只需生成一次**。后续处理任何图像时，脚本会自动加载此文件，跳过耗时的离线计算。

2.  **`precompensated_image_final.png`** (图像文件):
    *   这是**在线阶段的最终产物**——经过预补偿的图像。
    *   **注意**: 这张图像在电脑屏幕上直接观看时，可能会显得有些模糊、或带有奇怪的“振铃”效应。这是**正常且正确**的，因为它已经被“预先扭曲”，以抵消真实光学系统带来的模糊。

3.  **一个MATLAB图像窗口**:
    *   此窗口会并排显示处理前的**原始图像**和处理后的**预补偿图像**，便于您进行直观的比较。

