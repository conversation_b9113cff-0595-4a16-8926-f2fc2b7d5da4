function output_image = online_stage_auto(input_image, ...
    prekernel_map_lowres, grid_centers_lowres_coords)

    [img_h, img_w] = size(input_image);
    num_kernels = numel(prekernel_map_lowres);

    % --- Step 1: Generate all candidate images (same as before) ---
    fprintf('  - Step 1/2: Generating candidate images...\n');
    candidate_images = zeros(img_h, img_w, num_kernels);
    parfor i = 1:num_kernels
        candidate_images(:,:,i) = imfilter(input_image, prekernel_map_lowres{i}, 'replicate', 'conv', 'same');
    end

    % --- Step 2: Interpolate using real coordinates (FULLY VECTORIZED) ---
    fprintf('  - Step 2/2: Interpolating final image using real coordinates...\n');

    % Create the interpolant object. This object "knows" how to map
    % the scattered coordinates to the stack of candidate images.
    F = scatteredInterpolant(grid_centers_lowres_coords(:,1), ...
                             grid_centers_lowres_coords(:,2), ...
                             (1:num_kernels)', 'linear', 'none');

    % Create a query grid corresponding to every pixel of the UI image
    [Xq, Yq] = meshgrid(1:img_w, 1:img_h);

    % Query the interpolant to find the "image index" for every pixel
    % This will be a floating point number, e.g., 5.7
    image_indices = F(Xq, Yq);

    % Now we interpolate between the two nearest candidate images
    idx1 = floor(image_indices);
    idx2 = ceil(image_indices);
    
    % The interpolation weight is the fractional part of the index
    weight = image_indices - idx1;
    weight(isnan(weight)) = 0; % Handle pixels outside the convex hull
    idx1(isnan(idx1)) = 1;     % Prevent NaN indices
    idx2(isnan(idx2)) = 1;

    % Retrieve pixel values from the two bracketing candidate images
    V1 = candidate_images(sub2ind(size(candidate_images), Yq, Xq, idx1));
    V2 = candidate_images(sub2ind(size(candidate_images), Yq, Xq, idx2));

    % Perform final linear interpolation
    output_image = (1 - weight) .* V1 + weight .* V2;
    output_image(isnan(image_indices)) = input_image(isnan(image_indices)); % Keep original pixels if outside compensation area
end
