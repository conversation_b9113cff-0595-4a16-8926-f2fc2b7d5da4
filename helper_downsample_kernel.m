function low_res_kernel = helper_downsample_kernel(high_res_kernel, scale_factor)
    % Converts a high-res kernel to a low-res one by energy-preserving binning.
    
    [hr_h, hr_w] = size(high_res_kernel);
    
    % Pad with zeros if size is not perfectly divisible by scale_factor
    pad_h = mod(hr_h, scale_factor);
    pad_w = mod(hr_w, scale_factor);
    if pad_h ~= 0, pad_h = scale_factor - pad_h; end
    if pad_w ~= 0, pad_w = scale_factor - pad_w; end
    high_res_kernel = padarray(high_res_kernel, [pad_h, pad_w], 0, 'post');
    [hr_h, hr_w] = size(high_res_kernel);

    lr_h = hr_h / scale_factor;
    lr_w = hr_w / scale_factor;
    
    % A highly vectorized way to perform binning/summation
    reshaped_h = reshape(high_res_kernel, scale_factor, lr_h, hr_w);
    sum_h = squeeze(sum(reshaped_h, 1));
    reshaped_w = reshape(sum_h', scale_factor, lr_w, lr_h);
    sum_w = squeeze(sum(reshaped_w, 1));
    
    low_res_kernel = sum_w';
    
    % Normalize the final kernel to preserve image brightness after convolution
    kernel_sum = sum(low_res_kernel(:));
    if abs(kernel_sum) > 1e-9 % Avoid division by zero
        low_res_kernel = low_res_kernel / kernel_sum;
    end
end
