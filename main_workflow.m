% =========================================================================
% MAIN SCRIPT - HUD PRE-COMPENSATION WORKFLOW
% =========================================================================
% This script implements a complete workflow for HUD optical pre-compensation
% using spatially-varying deconvolution. It processes BSF (Beam Spread Function)
% data from optical simulation to generate pre-compensation kernels for real-time
% image processing.
%
% The workflow consists of:
% 1. Offline Stage: Generate pre-compensation kernel map from simulation data
% 2. Online Stage: Apply real-time compensation to UI images
%
% Author: HUD Pre-Compensation System
% Date: 2025
% =========================================================================

clear; clc; close all;

%% --- 1. CONFIGURATION (USER INPUT REQUIRED HERE) ---
fprintf('=== HUD Pre-Compensation Workflow ===\n');
fprintf('Step 1: Configuring workflow parameters...\n');

% -- Data Source Configuration --
% Choose ONE of the following data source formats:
% Option 1: Directory with individual .mat files (recommended, matches documentation)
bsf_data_folder = 'bsf_data';  % Directory containing bsf_1.mat, bsf_2.mat, ..., bsf_36.mat

% Option 2: Single text file with X, Y, Intensity columns (alternative format)
% simulation_data_file = 'simulation_data/full_simulation_output.txt';

% For this workflow, we'll use the documented .mat file format
data_source = bsf_data_folder;

% -- File Paths --
ui_image_path = 'ui_images/test_arrow.png';
output_map_file = 'prekernel_map_lowres_final.mat';  % Matches documentation
output_image_file = 'precompensated_image_final.png';  % Matches documentation

% -- Grid & Simulation Parameters --
grid_dims = [6, 6];           % 6x6 grid as per documentation
oversampling_rate = 3;        % 3x oversampling as per documentation
matrix_size = 31;             % 31x31 matrix size as per documentation

% -- Algorithm Tuning Parameters --
noise_ratio_step1 = 1e-5;     % Regularization for BSF -> PSF conversion
noise_ratio_step2 = 1e-3;     % Regularization for PSF -> PreKernel conversion

% -- Grid Center Coordinates --
% IMPORTANT: Provide the approximate center coordinates of the 36 BSF spots
% as they appear in the simulation space (high-resolution coordinates).
% This should be a 36x2 matrix [X1, Y1; X2, Y2; ...].
%
% The example below creates a 6x6 grid with 100-unit spacing starting at (50,50).
% REPLACE THIS WITH YOUR ACTUAL COORDINATES FROM THE SIMULATION.
fprintf('  - Generating example grid coordinates (REPLACE WITH ACTUAL DATA)...\n');
[grid_x, grid_y] = meshgrid(0:5, 0:5);
grid_centers_highres_coords = [grid_x(:) * 100 + 50, grid_y(:) * 100 + 50];

% Validate configuration
fprintf('  - Validating configuration...\n');
if size(grid_centers_highres_coords, 1) ~= prod(grid_dims)
    error('Grid coordinates must have %d rows for a %dx%d grid.', ...
        prod(grid_dims), grid_dims(1), grid_dims(2));
end

fprintf('  - Configuration complete. Using %s as data source.\n', data_source);

%% --- 2. OFFLINE STAGE ---
fprintf('\nStep 2: Starting Offline Stage (Kernel Map Generation)...\n');

% Check if pre-computed kernel map exists
if ~exist(output_map_file, 'file')
    fprintf('  - Kernel map not found. Starting offline computation...\n');

    % Validate data source exists
    if ~exist(data_source, 'file') && ~exist(data_source, 'dir')
        error('Data source "%s" does not exist. Please check the path and ensure data files are present.', data_source);
    end

    try
        % Run offline stage to generate kernel map
        offline_stage_auto(data_source, output_map_file, ...
            grid_dims, grid_centers_highres_coords, oversampling_rate, matrix_size, ...
            noise_ratio_step1, noise_ratio_step2);

        fprintf('  - Offline stage completed successfully.\n');

    catch ME
        fprintf('  - ERROR in offline stage: %s\n', ME.message);
        fprintf('  - Please check your data files and configuration.\n');
        rethrow(ME);
    end
else
    fprintf('  - Pre-computed kernel map "%s" found. Skipping offline stage.\n', output_map_file);
end

%% --- 3. ONLINE STAGE (REAL-TIME COMPENSATION) ---
fprintf('\nStep 3: Starting Online Stage (Image Compensation)...\n');

% Load pre-computed kernel map
try
    fprintf('  - Loading pre-computed kernel map...\n');
    map_data = load(output_map_file, 'prekernel_map_lowres', 'grid_centers_lowres_coords', 'grid_dims');

    % Validate loaded data
    if ~isfield(map_data, 'prekernel_map_lowres') || ~isfield(map_data, 'grid_centers_lowres_coords')
        error('Kernel map file is missing required variables.');
    end

    prekernel_map_lowres = map_data.prekernel_map_lowres;
    grid_centers_lowres_coords = map_data.grid_centers_lowres_coords;

    fprintf('  - Kernel map loaded successfully.\n');

catch ME
    error('Failed to load kernel map: %s', ME.message);
end

% Load and validate UI image
try
    fprintf('  - Loading UI image from "%s"...\n', ui_image_path);

    if ~exist(ui_image_path, 'file')
        error('UI image file "%s" does not exist.', ui_image_path);
    end

    ui_image = imread(ui_image_path);
    ui_image = im2double(ui_image);

    % Convert to grayscale if needed
    if size(ui_image, 3) > 1
        fprintf('  - Converting color image to grayscale...\n');
        ui_image = rgb2gray(ui_image);
    end

    fprintf('  - UI image loaded: %dx%d pixels.\n', size(ui_image, 1), size(ui_image, 2));

catch ME
    error('Failed to load UI image: %s', ME.message);
end

% Apply pre-compensation
try
    fprintf('  - Applying pre-compensation...\n');
    precompensated_image = online_stage_auto(ui_image, ...
        prekernel_map_lowres, grid_centers_lowres_coords);

    fprintf('  - Pre-compensation completed successfully.\n');

catch ME
    error('Failed to apply pre-compensation: %s', ME.message);
end

%% --- 4. SAVE & DISPLAY RESULTS ---
fprintf('\nStep 4: Saving and displaying results...\n');

% Save pre-compensated image
try
    imwrite(precompensated_image, output_image_file);
    fprintf('  - Pre-compensated image saved to "%s".\n', output_image_file);
catch ME
    warning('Failed to save output image: %s', ME.message);
end

% Display results
try
    fprintf('  - Displaying comparison results...\n');

    % Create figure for side-by-side comparison
    figure('Name', 'HUD Pre-Compensation Results', 'Position', [100, 100, 1200, 500]);

    % Original image
    subplot(1, 2, 1);
    imshow(ui_image);
    title('Original UI Image', 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('This is the input image');

    % Pre-compensated image
    subplot(1, 2, 2);
    imshow(precompensated_image);
    title('Pre-Compensated Image', 'FontSize', 14, 'FontWeight', 'bold');
    xlabel('This image is "pre-distorted" to compensate for optical blur');

    % Add overall title
    sgtitle('HUD Optical Pre-Compensation Results', 'FontSize', 16, 'FontWeight', 'bold');

    % Display processing summary
    fprintf('\n=== PROCESSING SUMMARY ===\n');
    fprintf('Input image size: %dx%d pixels\n', size(ui_image, 1), size(ui_image, 2));
    fprintf('Grid dimensions: %dx%d\n', grid_dims(1), grid_dims(2));
    fprintf('Oversampling rate: %dx\n', oversampling_rate);
    fprintf('Matrix size: %dx%d\n', matrix_size, matrix_size);
    fprintf('Noise ratios: %.1e (BSF->PSF), %.1e (PSF->PreKernel)\n', noise_ratio_step1, noise_ratio_step2);
    fprintf('Output files:\n');
    fprintf('  - Kernel map: %s\n', output_map_file);
    fprintf('  - Pre-compensated image: %s\n', output_image_file);

    fprintf('\nNOTE: The pre-compensated image may appear blurred or distorted\n');
    fprintf('when viewed on a computer screen. This is normal and expected.\n');
    fprintf('The image is designed to appear sharp when viewed through the\n');
    fprintf('actual HUD optical system.\n');

catch ME
    warning('Failed to display results: %s', ME.message);
end

fprintf('\n=== WORKFLOW COMPLETED SUCCESSFULLY ===\n');
