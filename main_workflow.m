% =========================================================================
% MAIN SCRIPT - FULLY AUTOMATED WORKFLOW
% =========================================================================
clear; clc; close all;

%% --- 1. CONFIGURATION (USER INPUT REQUIRED HERE) ---
fprintf('Step 1: Configuring workflow...\n');

% -- File Paths --
simulation_data_file = 'simulation_data/full_simulation_output.txt';
ui_image_path = 'ui_images/test_arrow.png';
output_map_file = 'prekernel_map_auto.mat';
output_image_file = 'precompensated_image_auto.png';

% -- Grid & Simulation Parameters --
grid_dims = [6, 6];       
oversampling_rate = 3;    
matrix_size = 31; % The size of the matrix to extract for each BSF

% -- Algorithm Tuning Parameters --
noise_ratio_step1 = 1e-5; % For BSF -> PSF
noise_ratio_step2 = 1e-3; % For PSF -> PreKernel

% !!! CRUCIAL USER INPUT !!!
% Provide the approximate center coordinates of the 36 BSF spots
% as they appear IN THE SIMULATION SPACE (high-resolution coordinates).
% This is a 36x2 matrix [X1, Y1; X2, Y2; ...].
% EXAMPLE:
grid_centers_highres_coords = ...
    kron( (0:5)'*100, [1,0] ) + kron( (0:5), [0,100] ) + 50;
% This example assumes spots are on a 100-unit grid starting at (50,50).
% YOU MUST REPLACE THIS WITH YOUR ACTUAL COORDINATES FROM THE SIMULATION.

%% --- 2. OFFLINE STAGE ---
fprintf('\nStep 2: Starting Fully Automated Offline Stage...\n');

if ~exist(output_map_file, 'file')
    offline_stage_auto(simulation_data_file, output_map_file, ...
        grid_dims, grid_centers_highres_coords, oversampling_rate, matrix_size, ...
        noise_ratio_step1, noise_ratio_step2);
else
    fprintf('  - Automated PreKernel map file "%s" already exists. Skipping.\n', output_map_file);
end

%% --- 3. ONLINE STAGE (AUTOMATED) ---
fprintf('\nStep 3: Starting Automated Online Stage...\n');

% The map file now contains the coordinates needed for automated mapping.
load(output_map_file, 'prekernel_map_lowres', 'grid_centers_lowres_coords');
ui_image = im2double(imread(ui_image_path));
if size(ui_image, 3) > 1, ui_image = rgb2gray(ui_image); end

precompensated_image = online_stage_auto(ui_image, ...
    prekernel_map_lowres, grid_centers_lowres_coords);

%% --- 4. SAVE & DISPLAY RESULTS ---
fprintf('\nStep 4: Saving and displaying results...\n');
imwrite(precompensated_image, output_image_file);
% ... (display code is the same)
