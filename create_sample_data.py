#!/usr/bin/env python3
"""
CREATE_SAMPLE_DATA - Generate sample BSF data and test image for HUD system

This script creates sample data files to test the HUD pre-compensation workflow:
1. Sample BSF data files (bsf_1.mat to bsf_36.mat) in bsf_data/ directory
2. Sample UI test image in ui_images/ directory

The generated BSF data simulates realistic optical blur patterns that vary
across a 6x6 grid, representing different field positions in the HUD.
"""

import numpy as np
import os
from scipy.io import savemat
from PIL import Image, ImageDraw
import matplotlib.pyplot as plt

def create_directories():
    """Create required directories if they don't exist."""
    os.makedirs('bsf_data', exist_ok=True)
    os.makedirs('ui_images', exist_ok=True)

def generate_bsf_data():
    """Generate sample BSF data files."""
    print('Generating sample BSF data files...')
    
    grid_dims = [6, 6]
    matrix_size = 31
    num_kernels = grid_dims[0] * grid_dims[1]
    
    # Create coordinate grids
    x = np.arange(matrix_size)
    y = np.arange(matrix_size)
    X, Y = np.meshgrid(x, y)
    center = (matrix_size - 1) / 2
    
    for i in range(num_kernels):
        # Calculate grid position (0-indexed)
        row = i // grid_dims[1]
        col = i % grid_dims[1]
        
        # Vary blur characteristics based on position
        # Simulate field-dependent aberrations
        sigma_x = 1.5 + 0.5 * abs(col - 2.5)  # Horizontal blur varies with column
        sigma_y = 1.5 + 0.3 * abs(row - 2.5)  # Vertical blur varies with row
        
        # Add some astigmatism (different blur in X and Y)
        rotation = (row - 2.5) * (col - 2.5) * 0.1  # Slight rotation based on position
        
        # Create anisotropic Gaussian
        X_rot = (X - center) * np.cos(rotation) - (Y - center) * np.sin(rotation)
        Y_rot = (X - center) * np.sin(rotation) + (Y - center) * np.cos(rotation)
        
        bsf_data = np.exp(-(X_rot**2 / (2 * sigma_x**2) + Y_rot**2 / (2 * sigma_y**2)))
        
        # Add some noise and asymmetry to make it more realistic
        noise_level = 0.05
        bsf_data = bsf_data + noise_level * np.random.randn(*bsf_data.shape)
        bsf_data = np.maximum(bsf_data, 0)  # Ensure non-negative
        
        # Add slight asymmetry (coma-like aberration)
        coma_strength = 0.1 * np.sqrt((row - 2.5)**2 + (col - 2.5)**2)
        coma_shift = coma_strength * np.array([col - 2.5, row - 2.5]) / 2.5
        
        if abs(coma_shift[0]) > 0.1 or abs(coma_shift[1]) > 0.1:
            # Apply slight shift to simulate coma
            from scipy.ndimage import shift
            bsf_shifted = shift(bsf_data, coma_shift, order=1, mode='constant', cval=0)
            bsf_data = 0.7 * bsf_data + 0.3 * bsf_shifted
        
        # Normalize
        bsf_data = bsf_data / np.sum(bsf_data)
        
        # Save to .mat file (MATLAB format)
        filename = f'bsf_data/bsf_{i+1}.mat'
        savemat(filename, {'bsf_data': bsf_data})
        
        if (i + 1) % 6 == 0:
            print(f'  Generated {i+1}/{num_kernels} BSF files...')
    
    print('Sample BSF data generation complete.')

def generate_test_image():
    """Generate sample UI test image."""
    print('Generating sample UI test image...')
    
    # Create a test image with various features
    img_size = (300, 200)  # Width x Height
    
    # Create image using PIL for better shape drawing
    img = Image.new('L', img_size, 0)  # Grayscale image, black background
    draw = ImageDraw.Draw(img)
    
    # Add arrow pointing right
    arrow_center = (150, 100)
    arrow_points = [
        (120, 90), (120, 110), (140, 110), (140, 120),
        (180, 100), (140, 80), (140, 90)
    ]
    draw.polygon(arrow_points, fill=255)
    
    # Add some rectangular features (simulating UI elements)
    rectangles = [
        (50, 50, 130, 70),   # x1, y1, x2, y2
        (200, 50, 260, 70),
        (80, 150, 180, 165),
    ]
    
    for rect in rectangles:
        draw.rectangle(rect, fill=200)
    
    # Add some circular features (simulating buttons or indicators)
    circles = [
        (250, 60, 15),   # x, y, radius
        (250, 140, 12),
        (50, 180, 10),
    ]
    
    for cx, cy, r in circles:
        draw.ellipse([cx-r, cy-r, cx+r, cy+r], fill=150)
    
    # Add some fine details (simulating text)
    for i in range(5):
        y_start = 30 + i * 25
        for j in range(8):
            x_start = 20 + j * 30
            if x_start + 8 <= img_size[0] and y_start + 12 <= img_size[1]:
                draw.rectangle([x_start, y_start, x_start+6, y_start+8], fill=100)
    
    # Convert to numpy array and normalize to [0, 1]
    test_image = np.array(img, dtype=np.float64) / 255.0
    
    # Apply slight Gaussian blur to avoid aliasing
    from scipy.ndimage import gaussian_filter
    test_image = gaussian_filter(test_image, sigma=0.5)
    
    # Save as PNG
    test_image_uint8 = (test_image * 255).astype(np.uint8)
    Image.fromarray(test_image_uint8, mode='L').save('ui_images/test_arrow.png')
    
    print('Sample UI test image saved to ui_images/test_arrow.png')

def main():
    """Main function to create all sample data."""
    print('Creating sample data for HUD pre-compensation system...')
    
    # Create directories
    create_directories()
    
    # Generate BSF data
    generate_bsf_data()
    
    # Generate test image
    generate_test_image()
    
    # Display summary
    print('\n=== SAMPLE DATA GENERATION COMPLETE ===')
    print('Created files:')
    print('  - 36 BSF data files in bsf_data/ directory')
    print('  - 1 test UI image in ui_images/ directory')
    print('\nYou can now run the main_workflow.m script to test the system.')
    print('Note: The grid coordinates in main_workflow.m are set to example values.')
    print('For real applications, replace them with actual simulation coordinates.')

if __name__ == '__main__':
    main()
